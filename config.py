#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件 - 统一管理所有参数
"""

# 摄像头配置
CAMERA_WIDTH = 320  # 摄像头宽度
CAMERA_HEIGHT = 240  # 摄像头高度

# 色块检测配置 (LAB色彩空间)
# 格式: [L_min, L_max, A_min, A_max, B_min, B_max]
GREEN_THRESHOLD = [[0, 80, -120, -10, 0, 30]]  # 绿色阈值
WHITE_THRESHOLD = [[80, 100, -10, 10, -10, 10]]  # 白色阈值
CURRENT_COLOR = "WHITE"  # 当前检测颜色: "GREEN" 或 "WHITE"

# 色块检测配置
BLOB_MIN_PIXELS = 500  # 最小色块像素数
BLOB_MAX_PIXELS = 50000  # 最大色块像素数

# 触摸屏配置
TOUCH_DEBOUNCE_MS = 200  # 触摸防抖时间(毫秒)

# 触摸按钮配置
BUTTON_RECT = [10, 200, 80, 30]  # 按钮矩形区域 [x, y, width, height]
BUTTON_DEBOUNCE_MS = 200  # 按钮防抖时间(毫秒)
ENABLE_TOUCH_BUTTON = True  # 是否启用触摸按钮功能

# 显示配置
SHOW_FPS = True  # 是否显示帧率
SHOW_DEBUG_INFO = True  # 是否显示调试信息

# 字体配置
FONT_PATHS = [  # 中文字体路径优先级列表
    "/maixapp/share/font/SourceHanSansCN-Regular.otf",
    "/maixapp/share/font/ppocr_keys_v1.ttf",
    "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
    "/system/fonts/DroidSansFallback.ttf"
]
FONT_SIZE = 16  # 字体大小
FORCE_ENGLISH = False  # 强制使用英文显示

# 性能配置
FRAME_DELAY_MS = 10  # 帧间延时(毫秒)
