#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件 - 统一管理所有参数
"""

# 摄像头配置
CAMERA_WIDTH = 320  # 摄像头宽度
CAMERA_HEIGHT = 240  # 摄像头高度

# 绿色检测配置 (LAB色彩空间)
# 格式: [L_min, L_max, A_min, A_max, B_min, B_max]
GREEN_THRESHOLD = [[0, 80, -120, -10, 0, 30]]  # 绿色阈值

# 色块检测配置
BLOB_MIN_PIXELS = 500  # 最小色块像素数
BLOB_MAX_PIXELS = 50000  # 最大色块像素数

# 触摸屏配置
TOUCH_DEBOUNCE_MS = 200  # 触摸防抖时间(毫秒)

# 显示配置
SHOW_FPS = True  # 是否显示帧率
SHOW_DEBUG_INFO = True  # 是否显示调试信息

# 性能配置
FRAME_DELAY_MS = 10  # 帧间延时(毫秒)
