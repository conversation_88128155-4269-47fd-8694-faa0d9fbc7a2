# MaixCAM 触摸按钮使用指南

## 触摸按钮布局

### 屏幕布局示意图

```
┌─────────────────────────────────────┐ ← 屏幕顶部 (y=0)
│  模式: 彩色                         │
│  触摸屏幕切换模式                   │
│  按钮切换LAB/二值化                 │
│  检测到 2 个白色色块                │
│  检测颜色: 白色                     │
│                                     │
│        摄像头画面区域               │
│                                     │
│    ┌─────┐  ← 绿色色块1             │
│    │     │                         │
│    └─────┘                         │
│                                     │
│              ┌─────┐  ← 绿色色块2   │
│              │     │               │
│              └─────┘               │
│                                     │
│                                     │
│ ┌────────┐                         │ ← 触摸按钮区域
│ │  LAB   │  ← 触摸按钮              │   (默认位置)
│ └────────┘                         │
└─────────────────────────────────────┘ ← 屏幕底部 (y=240)
 ↑                                   ↑
x=0                               x=320
```

### 按钮详细信息

#### 默认配置
- **位置**: 左下角
- **坐标**: (10, 200)
- **大小**: 80x30 像素
- **区域**: [10, 200, 80, 30] = [x, y, width, height]

#### 按钮状态
1. **LAB模式**: 
   - 背景色: 蓝色
   - 文字: "LAB" (白色)
   - 功能: 显示LAB色彩空间图像

2. **二值化模式**:
   - 背景色: 灰色
   - 文字: "Binary" 或 "二值化" (黑色)
   - 功能: 显示黑白二值化图像

## 操作方式

### 触摸区域功能

| 触摸区域 | 功能 | 模式切换 |
|----------|------|----------|
| 触摸按钮区域 | LAB ↔ 二值化 | lab_mode 切换 |
| 其他屏幕区域 | 彩色 ↔ 二值化 | binary_mode 切换 |

### 操作逻辑

1. **触摸按钮区域**:
   ```
   LAB模式 → 二值化模式 → LAB模式 → ...
   ```

2. **触摸其他区域**:
   ```
   彩色模式 → 二值化模式 → 彩色模式 → ...
   ```
   注意: 触摸其他区域会自动退出LAB模式

### 模式组合

| 当前状态 | 触摸按钮 | 触摸其他区域 | 结果状态 |
|----------|----------|--------------|----------|
| 彩色模式 | LAB模式 | 二值化模式 | LAB/二值化 |
| LAB模式 | 二值化模式 | 二值化模式 | 二值化 |
| 二值化模式 | LAB模式 | 彩色模式 | LAB/彩色 |

## 自定义配置

### 修改按钮位置

在 `config.py` 中修改：

```python
# 按钮位置配置 [x, y, width, height]
BUTTON_RECT = [10, 200, 80, 30]  # 左下角 (默认)
# BUTTON_RECT = [240, 10, 80, 30]   # 右上角
# BUTTON_RECT = [120, 200, 80, 30]  # 底部中央
# BUTTON_RECT = [10, 10, 80, 30]    # 左上角
```

### 常用位置预设

#### 左上角
```python
BUTTON_RECT = [10, 10, 80, 30]
```

#### 右上角
```python
BUTTON_RECT = [230, 10, 80, 30]  # 320-80-10 = 230
```

#### 底部中央
```python
BUTTON_RECT = [120, 200, 80, 30]  # (320-80)/2 = 120
```

#### 右下角
```python
BUTTON_RECT = [230, 200, 80, 30]
```

### 调整按钮大小

```python
# 小按钮
BUTTON_RECT = [10, 200, 60, 25]

# 大按钮
BUTTON_RECT = [10, 200, 100, 40]

# 方形按钮
BUTTON_RECT = [10, 200, 50, 50]
```

## 防抖配置

### 防抖时间设置

```python
BUTTON_DEBOUNCE_MS = 200  # 默认200毫秒

# 快速响应 (可能误触)
BUTTON_DEBOUNCE_MS = 100

# 慢速响应 (防误触)
BUTTON_DEBOUNCE_MS = 300
```

### 防抖原理

- **作用**: 防止触摸时的机械抖动造成多次触发
- **机制**: 在防抖时间内忽略后续触摸
- **建议**: 根据使用习惯调整，一般100-300ms

## 禁用按钮

### 完全禁用

```python
ENABLE_TOUCH_BUTTON = False
```

禁用后：
- 不显示触摸按钮
- 触摸任意位置都是彩色/二值化切换
- 无法进入LAB模式

### 临时禁用

在程序运行时，可以通过修改变量临时禁用：

```python
# 在main.py中添加
self.button_enabled = False  # 临时禁用
```

## 故障排除

### 常见问题

1. **按钮不显示**
   - 检查 `ENABLE_TOUCH_BUTTON = True`
   - 确认按钮位置在屏幕范围内
   - 检查按钮大小是否合理

2. **触摸无响应**
   - 确认触摸位置在按钮区域内
   - 检查防抖时间设置
   - 运行 `python test_touch_button.py` 测试

3. **按钮位置不对**
   - 检查 `BUTTON_RECT` 配置
   - 确认坐标系统 (左上角为原点)
   - 注意宽高不要超出屏幕边界

4. **误触频繁**
   - 增加防抖时间
   - 调整按钮大小
   - 重新定位按钮位置

### 调试方法

#### 坐标测试
```bash
python test_touch_button.py coord
```
显示触摸坐标，帮助确定按钮位置

#### 功能测试
```bash
python test_touch_button.py
```
完整测试按钮功能和响应

## 技术细节

### 坐标系统
- **原点**: 屏幕左上角 (0, 0)
- **X轴**: 向右递增，最大320
- **Y轴**: 向下递增，最大240
- **单位**: 像素

### 碰撞检测算法
```python
def is_point_in_rect(x, y, rect):
    rx, ry, rw, rh = rect
    return rx <= x <= rx + rw and ry <= y <= ry + rh
```

### 按钮绘制
- **背景**: 填充矩形
- **边框**: 2像素白色边框
- **文字**: 居中显示，自适应颜色

## 扩展功能

### 多按钮支持

可以扩展支持多个按钮：

```python
# 配置多个按钮
BUTTON1_RECT = [10, 200, 80, 30]   # LAB/二值化
BUTTON2_RECT = [100, 200, 80, 30]  # 颜色切换
BUTTON3_RECT = [190, 200, 80, 30]  # 退出程序
```

### 按钮样式自定义

```python
# 自定义按钮颜色
LAB_BUTTON_COLOR = image.COLOR_BLUE
BINARY_BUTTON_COLOR = image.COLOR_GRAY
BUTTON_BORDER_COLOR = image.COLOR_WHITE
```

### 手势支持

可以扩展支持滑动手势：
- 左滑: 切换颜色
- 右滑: 切换模式
- 双击: 重置设置
