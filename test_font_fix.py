#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字体修复测试脚本
快速验证中文字体问题是否已解决
"""

try:
    from maix import image, display, app, time
    
    def test_font_display():
        """测试字体显示"""
        print("开始字体显示测试...")
        
        # 字体路径列表
        font_paths = [
            "/maixapp/share/font/SourceHanSansCN-Regular.otf",
            "/maixapp/share/font/ppocr_keys_v1.ttf",
            "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
            "/system/fonts/DroidSansFallback.ttf"
        ]
        
        disp = display.Display()
        font_loaded = False
        
        # 尝试加载字体
        for font_path in font_paths:
            try:
                image.load_font("test_font", font_path, size=20)
                image.set_default_font("test_font")
                print(f"✓ 成功加载字体: {font_path}")
                font_loaded = True
                break
            except Exception as e:
                print(f"✗ 字体加载失败 {font_path}: {e}")
                continue
        
        # 创建测试图像
        img = image.Image(disp.width(), disp.height())
        img.draw_rect(0, 0, img.width(), img.height(), image.COLOR_BLACK, -1)
        
        if font_loaded:
            # 测试中文显示
            test_texts = [
                "中文字体测试",
                "绿色色块检测",
                "触摸屏切换模式",
                "字体加载成功!"
            ]
            
            y_pos = 20
            for text in test_texts:
                img.draw_string(10, y_pos, text, image.COLOR_WHITE)
                y_pos += 30
            
            img.draw_string(10, y_pos + 20, "Font test successful!", image.COLOR_GREEN)
            print("✓ 中文字体显示测试成功")
        else:
            # 使用英文显示
            test_texts = [
                "Font loading failed",
                "Using English mode",
                "Green blob detection",
                "Touch to switch mode"
            ]
            
            y_pos = 20
            for text in test_texts:
                img.draw_string(10, y_pos, text, image.COLOR_WHITE)
                y_pos += 30
            
            img.draw_string(10, y_pos + 20, "English mode active", image.COLOR_YELLOW)
            print("! 使用英文模式显示")
        
        # 显示测试结果
        disp.show(img)
        print("测试图像已显示在屏幕上")
        print("按Ctrl+C退出测试")
        
        # 保持显示5秒
        for i in range(50):
            if app.need_exit():
                break
            time.sleep_ms(100)
        
        return font_loaded
    
    def main():
        """主函数"""
        print("=== 字体修复验证测试 ===")
        print()
        
        result = test_font_display()
        
        print()
        if result:
            print("✓ 字体问题已解决!")
            print("现在可以正常显示中文了")
        else:
            print("! 字体问题未完全解决")
            print("程序将使用英文模式运行")
            print("如需中文显示，请运行: python install_font.py")
        
        print()
        print("测试完成")
    
    if __name__ == "__main__":
        main()
        
except ImportError:
    print("错误: 无法导入maix模块")
    print("请确保在MaixCAM设备上运行此脚本")
except KeyboardInterrupt:
    print("\n用户中断测试")
except Exception as e:
    print(f"测试异常: {e}")
