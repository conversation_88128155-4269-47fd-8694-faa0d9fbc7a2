#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
阈值管理工具
用于保存、加载和管理自定义阈值
"""

import json
import os
from datetime import datetime

class ThresholdManager:
    """阈值管理器"""
    
    def __init__(self, filename="custom_thresholds.json"):
        self.filename = filename
        self.thresholds = self.load_thresholds()
    
    def load_thresholds(self):
        """加载阈值文件"""
        try:
            if os.path.exists(self.filename):
                with open(self.filename, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                return {}
        except Exception as e:
            print(f"加载阈值文件失败: {e}")
            return {}
    
    def save_thresholds(self):
        """保存阈值到文件"""
        try:
            with open(self.filename, 'w', encoding='utf-8') as f:
                json.dump(self.thresholds, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"保存阈值文件失败: {e}")
            return False
    
    def add_threshold(self, name, threshold, description="", color_type="AUTO"):
        """添加新阈值"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        threshold_data = {
            "threshold": threshold,
            "description": description,
            "color_type": color_type,
            "created_time": timestamp,
            "usage_count": 0
        }
        
        self.thresholds[name] = threshold_data
        return self.save_thresholds()
    
    def get_threshold(self, name):
        """获取指定阈值"""
        if name in self.thresholds:
            # 增加使用计数
            self.thresholds[name]["usage_count"] += 1
            self.save_thresholds()
            return self.thresholds[name]["threshold"]
        return None
    
    def list_thresholds(self):
        """列出所有阈值"""
        return list(self.thresholds.keys())
    
    def delete_threshold(self, name):
        """删除阈值"""
        if name in self.thresholds:
            del self.thresholds[name]
            return self.save_thresholds()
        return False
    
    def get_threshold_info(self, name):
        """获取阈值详细信息"""
        return self.thresholds.get(name, None)
    
    def export_threshold_to_config(self, name, color_type):
        """导出阈值到配置文件"""
        threshold = self.get_threshold(name)
        if not threshold:
            return False
        
        try:
            # 读取配置文件
            with open('config.py', 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # 更新对应的阈值
            target_line = f"{color_type}_THRESHOLD"
            updated = False
            
            for i, line in enumerate(lines):
                if line.strip().startswith(target_line):
                    lines[i] = f"{target_line} = {threshold}  # 从{name}导入\n"
                    updated = True
                    break
            
            if updated:
                # 写回配置文件
                with open('config.py', 'w', encoding='utf-8') as f:
                    f.writelines(lines)
                print(f"阈值 '{name}' 已导出到 {target_line}")
                return True
            else:
                print(f"未找到 {target_line} 配置项")
                return False
                
        except Exception as e:
            print(f"导出阈值失败: {e}")
            return False

def main():
    """主函数 - 阈值管理命令行工具"""
    manager = ThresholdManager()
    
    print("=== 阈值管理工具 ===")
    print("命令:")
    print("  list - 列出所有阈值")
    print("  info <name> - 显示阈值详细信息")
    print("  delete <name> - 删除阈值")
    print("  export <name> <GREEN|WHITE> - 导出阈值到配置文件")
    print("  quit - 退出")
    print()
    
    while True:
        try:
            cmd = input("请输入命令: ").strip().split()
            if not cmd:
                continue
            
            if cmd[0] == "quit":
                break
            elif cmd[0] == "list":
                thresholds = manager.list_thresholds()
                if thresholds:
                    print("已保存的阈值:")
                    for i, name in enumerate(thresholds, 1):
                        info = manager.get_threshold_info(name)
                        print(f"  {i}. {name} - {info['description']} (使用{info['usage_count']}次)")
                else:
                    print("没有保存的阈值")
            
            elif cmd[0] == "info" and len(cmd) > 1:
                name = cmd[1]
                info = manager.get_threshold_info(name)
                if info:
                    print(f"阈值名称: {name}")
                    print(f"阈值数据: {info['threshold']}")
                    print(f"描述: {info['description']}")
                    print(f"颜色类型: {info['color_type']}")
                    print(f"创建时间: {info['created_time']}")
                    print(f"使用次数: {info['usage_count']}")
                else:
                    print(f"未找到阈值: {name}")
            
            elif cmd[0] == "delete" and len(cmd) > 1:
                name = cmd[1]
                if manager.delete_threshold(name):
                    print(f"已删除阈值: {name}")
                else:
                    print(f"删除失败或未找到阈值: {name}")
            
            elif cmd[0] == "export" and len(cmd) > 2:
                name = cmd[1]
                color_type = cmd[2].upper()
                if color_type in ["GREEN", "WHITE"]:
                    if manager.export_threshold_to_config(name, color_type):
                        print("导出成功")
                    else:
                        print("导出失败")
                else:
                    print("颜色类型必须是 GREEN 或 WHITE")
            
            else:
                print("无效命令")
        
        except KeyboardInterrupt:
            print("\n退出程序")
            break
        except Exception as e:
            print(f"命令执行错误: {e}")

if __name__ == "__main__":
    main()
