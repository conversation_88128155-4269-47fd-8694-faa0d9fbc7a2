#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
颜色切换工具
快速切换检测颜色（绿色/白色）
"""

import os
import sys

def read_config():
    """读取当前配置"""
    try:
        with open('config.py', 'r', encoding='utf-8') as f:
            content = f.read()
        return content
    except FileNotFoundError:
        print("错误: 找不到config.py文件")
        return None
    except Exception as e:
        print(f"读取配置文件错误: {e}")
        return None

def write_config(content):
    """写入配置"""
    try:
        with open('config.py', 'w', encoding='utf-8') as f:
            f.write(content)
        return True
    except Exception as e:
        print(f"写入配置文件错误: {e}")
        return False

def get_current_color(content):
    """获取当前检测颜色"""
    for line in content.split('\n'):
        if line.strip().startswith('CURRENT_COLOR'):
            if '"WHITE"' in line:
                return "WHITE"
            elif '"GREEN"' in line:
                return "GREEN"
    return "UNKNOWN"

def switch_color(content, new_color):
    """切换检测颜色"""
    lines = content.split('\n')
    for i, line in enumerate(lines):
        if line.strip().startswith('CURRENT_COLOR'):
            lines[i] = f'CURRENT_COLOR = "{new_color}"  # 当前检测颜色: "GREEN" 或 "WHITE"'
            break
    return '\n'.join(lines)

def main():
    """主函数"""
    print("=== MaixCAM 颜色切换工具 ===")
    print()
    
    # 读取当前配置
    content = read_config()
    if content is None:
        return
    
    # 获取当前颜色
    current_color = get_current_color(content)
    color_name = {"WHITE": "白色", "GREEN": "绿色", "UNKNOWN": "未知"}
    
    print(f"当前检测颜色: {color_name.get(current_color, '未知')}")
    print()
    
    if current_color == "UNKNOWN":
        print("错误: 无法识别当前配置的颜色")
        return
    
    # 提供切换选项
    print("请选择要切换的颜色:")
    print("1. 绿色 (GREEN)")
    print("2. 白色 (WHITE)")
    print("3. 退出")
    print()
    
    try:
        choice = input("请输入选择 (1-3): ").strip()
        
        if choice == "1":
            new_color = "GREEN"
            new_color_name = "绿色"
        elif choice == "2":
            new_color = "WHITE"
            new_color_name = "白色"
        elif choice == "3":
            print("退出程序")
            return
        else:
            print("无效选择")
            return
        
        # 检查是否需要切换
        if current_color == new_color:
            print(f"当前已经是{new_color_name}检测模式")
            return
        
        # 执行切换
        new_content = switch_color(content, new_color)
        
        if write_config(new_content):
            print(f"✓ 成功切换到{new_color_name}检测模式")
            print()
            print("配置已更新，重新运行main.py生效:")
            print("python main.py")
        else:
            print("✗ 切换失败")
            
    except KeyboardInterrupt:
        print("\n用户取消操作")
    except Exception as e:
        print(f"操作异常: {e}")

if __name__ == "__main__":
    main()
