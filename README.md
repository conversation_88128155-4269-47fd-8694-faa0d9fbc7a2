# MaixCAM 绿色色块识别与触摸屏切换程序

## 功能特性

1. **绿色色块识别** - 实时检测摄像头画面中的绿色色块
2. **触摸屏切换** - 触摸屏幕可在彩色模式和二值化模式间切换
3. **MaixCAM支持** - 专为MaixCAM设备优化

## 主要功能

### 绿色色块检测
- 使用LAB色彩空间进行精确的绿色检测
- 可调节的最小像素阈值过滤小噪点
- 实时显示检测到的色块数量和位置
- 绘制绿色边框和红色中心点标记

### 触摸屏交互
- 触摸屏幕任意位置切换显示模式
- 防抖处理避免误触
- 实时显示当前模式状态

### 双模式显示
- **彩色模式**: 显示原始彩色图像和检测结果
- **二值化模式**: 显示黑白二值化图像和检测结果

## 配置参数

```python
CAMERA_WIDTH = 320          # 摄像头宽度
CAMERA_HEIGHT = 240         # 摄像头高度  
GREEN_THRESHOLD = [[0, 80, -120, -10, 0, 30]]  # 绿色阈值(LAB)
BLOB_MIN_PIXELS = 500       # 最小色块像素数
TOUCH_DEBOUNCE_MS = 200     # 触摸防抖时间(毫秒)
```

## 文件结构

- `main.py` - 主程序文件
- `config.py` - 配置参数文件
- `test_green_detection.py` - 测试脚本
- `README.md` - 说明文档

## 使用方法

1. 将所有文件上传到MaixCAM设备
2. 运行主程序：`python main.py`
3. 将绿色物体放在摄像头前进行检测
4. 触摸屏幕切换彩色/二值化显示模式

### 参数调整

编辑`config.py`文件可调整以下参数：
- 摄像头分辨率
- 绿色检测阈值
- 最小/最大色块大小
- 触摸防抖时间
- 显示选项

### 测试功能

运行测试脚本验证检测性能：
```bash
python test_green_detection.py
```

## 技术实现

### 核心类：GreenBlobDetector
- `__init__()`: 初始化摄像头、显示屏、触摸屏
- `detect_green_blobs()`: 检测绿色色块
- `draw_blob_info()`: 绘制检测结果
- `handle_touch()`: 处理触摸事件
- `process_image()`: 图像处理和模式切换
- `run()`: 主运行循环

### 依赖库
- `maix.camera`: 摄像头控制
- `maix.display`: 显示屏控制  
- `maix.image`: 图像处理
- `maix.touchscreen`: 触摸屏控制
- `maix.app`: 应用程序控制
- `maix.time`: 时间控制

## 注意事项

1. 确保MaixCAM设备已正确连接摄像头和触摸屏
2. 绿色阈值可根据实际环境光线调整
3. 最小像素数可根据检测精度需求调整
4. 程序支持优雅退出(Ctrl+C)

## 故障排除

- 如果检测不准确，请调整GREEN_THRESHOLD参数
- 如果触摸不灵敏，请调整TOUCH_DEBOUNCE_MS参数
- 如果性能不佳，请降低摄像头分辨率或增加延时
