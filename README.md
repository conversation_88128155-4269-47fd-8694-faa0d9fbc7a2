# MaixCAM 色块识别与触摸屏切换程序

## 功能特性

1. **多色块识别** - 实时检测摄像头画面中的绿色或白色色块
2. **触摸屏切换** - 触摸屏幕可在彩色模式和二值化模式间切换
3. **按键切换** - 物理按键可在LAB模式和二值化模式间切换
4. **MaixCAM支持** - 专为MaixCAM设备优化
5. **智能字体** - 自动检测中文字体，支持中英文显示

## 主要功能

### 色块检测
- 支持绿色和白色色块检测（可在config.py中配置）
- 使用LAB色彩空间进行精确的颜色检测
- 可调节的最小像素阈值过滤小噪点
- 实时显示检测到的色块数量和位置
- 根据检测颜色自动调整边框颜色（绿色用绿框，白色用蓝框）

### 触摸屏交互
- 触摸屏幕任意位置切换显示模式
- 防抖处理避免误触
- 实时显示当前模式状态

### 按键交互
- 物理按键切换LAB/二值化显示模式
- 可配置按键引脚和防抖时间
- 支持启用/禁用按键功能

### 多模式显示
- **彩色模式**: 显示原始彩色图像和检测结果
- **二值化模式**: 显示黑白二值化图像和检测结果
- **LAB模式**: 显示LAB色彩空间图像和检测结果

## 配置参数

```python
CAMERA_WIDTH = 320          # 摄像头宽度
CAMERA_HEIGHT = 240         # 摄像头高度
GREEN_THRESHOLD = [[0, 80, -120, -10, 0, 30]]  # 绿色阈值(LAB)
WHITE_THRESHOLD = [[80, 100, -10, 10, -10, 10]]  # 白色阈值(LAB)
CURRENT_COLOR = "WHITE"     # 当前检测颜色: "GREEN" 或 "WHITE"
BLOB_MIN_PIXELS = 500       # 最小色块像素数
TOUCH_DEBOUNCE_MS = 200     # 触摸防抖时间(毫秒)
BUTTON_PIN = "A19"          # 按键GPIO引脚
BUTTON_DEBOUNCE_MS = 200    # 按键防抖时间(毫秒)
ENABLE_BUTTON = True        # 是否启用按键功能
```

## 文件结构

- `main.py` - 主程序文件
- `config.py` - 配置参数文件
- `test_color_detection.py` - 色块检测测试脚本
- `test_green_detection.py` - 绿色检测测试脚本（旧版）
- `install_font.py` - 字体安装工具
- `test_font_fix.py` - 字体修复测试脚本
- `switch_color.py` - 颜色切换工具
- `test_button.py` - 按键功能测试脚本
- `BUTTON_WIRING.md` - 按键接线说明文档
- `README.md` - 说明文档

## 使用方法

1. 将所有文件上传到MaixCAM设备
2. 运行主程序：`python main.py`
3. 将绿色或白色物体放在摄像头前进行检测
4. 触摸屏幕切换彩色/二值化显示模式
5. 按下物理按键切换LAB/二值化显示模式（需要接线，参考BUTTON_WIRING.md）

### 参数调整

编辑`config.py`文件可调整以下参数：
- 摄像头分辨率
- 绿色/白色检测阈值
- 当前检测颜色（GREEN/WHITE）
- 最小/最大色块大小
- 触摸防抖时间
- 按键引脚和防抖时间
- 按键功能启用/禁用
- 字体配置
- 显示选项

### 颜色切换

#### 方法1：使用切换工具（推荐）
```bash
python switch_color.py
```
按提示选择要检测的颜色（绿色/白色）

#### 方法2：手动修改配置
修改`config.py`中的`CURRENT_COLOR`：
```python
CURRENT_COLOR = "WHITE"  # 检测白色
# 或
CURRENT_COLOR = "GREEN"  # 检测绿色
```

### 测试功能

运行测试脚本验证各项功能：
```bash
python test_color_detection.py  # 测试当前配置的颜色
python test_font_fix.py         # 测试字体显示
python test_button.py           # 测试按键功能
python test_button.py simple    # 简单按键测试（仅控制台）
```

## 技术实现

### 核心类：ColorBlobDetector
- `__init__()`: 初始化摄像头、显示屏、触摸屏、按键，加载字体
- `_init_button()`: 初始化物理按键GPIO
- `_load_font()`: 智能加载中文字体
- `get_text()`: 中英文文本切换
- `detect_color_blobs()`: 检测指定颜色色块
- `draw_blob_info()`: 绘制检测结果（自适应颜色）
- `handle_touch()`: 处理触摸事件
- `handle_button()`: 处理按键事件
- `process_image()`: 图像处理和模式切换（支持LAB/二值化/彩色）
- `run()`: 主运行循环

### 依赖库
- `maix.camera`: 摄像头控制
- `maix.display`: 显示屏控制  
- `maix.image`: 图像处理
- `maix.touchscreen`: 触摸屏控制
- `maix.app`: 应用程序控制
- `maix.time`: 时间控制

## 注意事项

1. 确保MaixCAM设备已正确连接摄像头和触摸屏
2. 绿色阈值可根据实际环境光线调整
3. 最小像素数可根据检测精度需求调整
4. 程序支持优雅退出(Ctrl+C)

## 中文字体问题解决

### 问题现象
如果触摸屏显示"???"而不是中文字符，说明设备缺少中文字体支持。

### 解决方案

#### 方法1：自动检测和切换
程序已内置字体检测功能，会自动：
1. 尝试加载常见中文字体路径
2. 如果加载失败，自动切换为英文显示
3. 在控制台显示字体加载状态

#### 方法2：手动安装字体
1. 运行字体检测工具：
```bash
python install_font.py
```

2. 手动复制字体文件到以下目录之一：
- `/maixapp/share/font/`
- `/usr/share/fonts/truetype/`
- `/system/fonts/`

3. 推荐字体文件：
- `SourceHanSansCN-Regular.otf` (思源黑体)
- `NotoSansCJK-Regular.ttc` (Noto字体)
- `DroidSansFallback.ttf` (Android字体)

#### 方法3：使用英文模式
如果无法安装中文字体，程序会自动切换为英文显示模式，功能完全正常。

## 故障排除

### 显示问题
- **中文显示为"???"**: 运行`python install_font.py`检查字体
- **程序自动使用英文**: 正常现象，说明中文字体未安装

### 检测问题
- **绿色检测不准确**: 调整`config.py`中的`GREEN_THRESHOLD`参数
- **白色检测不准确**: 调整`config.py`中的`WHITE_THRESHOLD`参数
- **想切换检测颜色**: 修改`CURRENT_COLOR`为"GREEN"或"WHITE"
- **触摸不灵敏**: 调整`TOUCH_DEBOUNCE_MS`参数
- **按键不响应**: 检查`BUTTON_PIN`配置和硬件连接
- **按键误触**: 调整`BUTTON_DEBOUNCE_MS`参数
- **性能不佳**: 降低摄像头分辨率或增加`FRAME_DELAY_MS`

### 按键配置说明
- **默认引脚**: A19（可在config.py中修改）
- **按键逻辑**: 按下时为低电平，释放时为高电平
- **连接方式**: 按键一端接GPIO引脚，另一端接GND
- **禁用按键**: 设置`ENABLE_BUTTON = False`

### 阈值调整建议
- **绿色阈值**: `[[0, 80, -120, -10, 0, 30]]` 适用于标准绿色
- **白色阈值**: `[[80, 100, -10, 10, -10, 10]]` 适用于纯白色
- 根据环境光线和物体颜色微调L、A、B值范围
