#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
阈值获取工具测试脚本
用于测试脱机取阈值功能
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from maix import camera, display, image, touchscreen, app, time
    from config import (THRESHOLD_RECT, SAMPLE_RECT_SIZE, THRESHOLD_SAMPLES, 
                       THRESHOLD_MARGIN, ENABLE_THRESHOLD_TOOL)
    
    def calculate_threshold_from_region(img, x, y, size):
        """从指定区域计算LAB阈值"""
        try:
            # 确保采样区域在图像范围内
            x1 = max(0, x - size // 2)
            y1 = max(0, y - size // 2)
            x2 = min(img.width(), x + size // 2)
            y2 = min(img.height(), y + size // 2)
            
            # 转换为LAB色彩空间
            lab_img = img.to_format(image.Format.FMT_LAB888)
            
            # 采样区域内的像素
            l_values = []
            a_values = []
            b_values = []
            
            # 采样策略：在区域内均匀采样
            step = max(1, (x2 - x1) // 8)  # 8x8网格采样
            for py in range(y1, y2, step):
                for px in range(x1, x2, step):
                    try:
                        # 获取LAB值
                        pixel = lab_img.get_pixel(px, py)
                        l_values.append(pixel[0])
                        a_values.append(pixel[1])
                        b_values.append(pixel[2])
                    except:
                        continue
            
            if len(l_values) < 10:  # 采样点太少
                return None
            
            # 计算统计值
            l_min, l_max = min(l_values), max(l_values)
            a_min, a_max = min(a_values), max(a_values)
            b_min, b_max = min(b_values), max(b_values)
            
            # 添加边距以提高检测稳定性
            l_range = l_max - l_min
            a_range = a_max - a_min
            b_range = b_max - b_min
            
            l_margin = max(THRESHOLD_MARGIN, l_range // 4)
            a_margin = max(THRESHOLD_MARGIN, a_range // 4)
            b_margin = max(THRESHOLD_MARGIN, b_range // 4)
            
            # 生成阈值
            threshold = [[
                max(0, l_min - l_margin), min(255, l_max + l_margin),
                max(-128, a_min - a_margin), min(127, a_max + a_margin),
                max(-128, b_min - b_margin), min(127, b_max + b_margin)
            ]]
            
            return threshold, {
                'l_range': (l_min, l_max),
                'a_range': (a_min, a_max),
                'b_range': (b_min, b_max),
                'samples': len(l_values)
            }
            
        except Exception as e:
            print(f"阈值计算错误: {e}")
            return None, None
    
    def is_point_in_rect(x, y, rect):
        """检查点是否在矩形内"""
        rx, ry, rw, rh = rect
        return rx <= x <= rx + rw and ry <= y <= ry + rh
    
    def test_threshold_tool():
        """测试阈值获取工具"""
        print("开始测试阈值获取工具...")
        print(f"阈值按钮区域: {THRESHOLD_RECT}")
        print(f"采样矩形大小: {SAMPLE_RECT_SIZE}x{SAMPLE_RECT_SIZE}")
        
        if not ENABLE_THRESHOLD_TOOL:
            print("阈值获取工具已禁用，请在config.py中启用")
            return False
        
        try:
            # 初始化设备
            cam = camera.Camera(320, 240)
            disp = display.Display()
            ts = touchscreen.TouchScreen()
            
            # 测试变量
            threshold_mode = False
            sample_rect = None
            custom_threshold = None
            threshold_count = 0
            
            print("阈值获取测试开始，按Ctrl+C退出...")
            print("1. 触摸阈值按钮进入阈值获取模式")
            print("2. 在阈值模式下触摸要采样的区域")
            
            while not app.need_exit():
                # 读取摄像头图像
                img = cam.read()
                x, y, pressed = ts.read()
                
                if pressed:
                    # 检查是否触摸了阈值按钮
                    if is_point_in_rect(x, y, THRESHOLD_RECT):
                        threshold_mode = not threshold_mode
                        if threshold_mode:
                            sample_rect = None
                            print("进入阈值获取模式")
                        else:
                            print("退出阈值获取模式")
                    
                    # 阈值获取模式下的区域选择
                    elif threshold_mode:
                        sample_rect = [x, y]
                        print(f"选择采样区域: ({x}, {y})")
                        
                        # 计算阈值
                        result = calculate_threshold_from_region(img, x, y, SAMPLE_RECT_SIZE)
                        if result[0]:
                            threshold, stats = result
                            custom_threshold = threshold
                            threshold_count += 1
                            
                            print(f"阈值 #{threshold_count}:")
                            print(f"  LAB阈值: {threshold[0]}")
                            print(f"  L范围: {stats['l_range']}")
                            print(f"  A范围: {stats['a_range']}")
                            print(f"  B范围: {stats['b_range']}")
                            print(f"  采样点数: {stats['samples']}")
                            print("阈值计算完成")
                            
                            threshold_mode = False
                            sample_rect = None
                        else:
                            print("阈值计算失败，请重新选择区域")
                
                # 绘制界面
                display_img = img.copy()
                
                # 绘制阈值按钮
                tx, ty, tw, th = THRESHOLD_RECT
                button_color = image.COLOR_RED if threshold_mode else image.COLOR_GREEN
                display_img.draw_rect(tx, ty, tw, th, button_color, -1)
                display_img.draw_rect(tx, ty, tw, th, image.COLOR_WHITE, 2)
                
                button_text = "Get Threshold"
                text_x = tx + (tw - len(button_text) * 6) // 2
                text_y = ty + (th - 12) // 2
                display_img.draw_string(text_x, text_y, button_text, image.COLOR_WHITE)
                
                # 绘制采样矩形
                if threshold_mode and sample_rect:
                    sx, sy = sample_rect
                    size = SAMPLE_RECT_SIZE
                    
                    x1 = max(0, sx - size // 2)
                    y1 = max(0, sy - size // 2)
                    x2 = min(img.width(), sx + size // 2)
                    y2 = min(img.height(), sy + size // 2)
                    
                    # 绘制采样矩形
                    display_img.draw_rect(x1, y1, x2 - x1, y2 - y1, image.COLOR_YELLOW, 3)
                    
                    # 绘制中心十字线
                    display_img.draw_line(sx - 10, sy, sx + 10, sy, image.COLOR_YELLOW, 2)
                    display_img.draw_line(sx, sy - 10, sx, sy + 10, image.COLOR_YELLOW, 2)
                
                # 显示信息
                y_pos = 10
                info_texts = [
                    f"Threshold Tool Test",
                    f"Mode: {'Threshold' if threshold_mode else 'Normal'}",
                    f"Thresholds Generated: {threshold_count}",
                    f"Touch Position: ({x}, {y})" if pressed else "No Touch",
                    "Touch threshold button to toggle mode"
                ]
                
                for text in info_texts:
                    color = image.COLOR_GREEN if "Mode:" in text else image.COLOR_WHITE
                    display_img.draw_string(10, y_pos, text, color)
                    y_pos += 20
                
                # 显示当前阈值
                if custom_threshold:
                    threshold_text = f"Current: {custom_threshold[0]}"
                    display_img.draw_string(10, y_pos, threshold_text, image.COLOR_YELLOW)
                
                # 测试阈值效果
                if custom_threshold:
                    # 使用自定义阈值进行色块检测
                    blobs = img.find_blobs(custom_threshold, pixels_threshold=100)
                    for blob in blobs:
                        display_img.draw_rect(blob[0], blob[1], blob[2], blob[3], image.COLOR_BLUE, 2)
                
                disp.show(display_img)
                time.sleep_ms(10)
                
            print(f"\n测试完成! 总生成阈值数: {threshold_count}")
            return True
            
        except Exception as e:
            print(f"✗ 阈值工具测试失败: {e}")
            return False
    
    def main():
        """主函数"""
        print("=== MaixCAM 阈值获取工具测试 ===")
        print()
        
        test_threshold_tool()
    
    if __name__ == "__main__":
        main()
        
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保在MaixCAM设备上运行此测试")
except KeyboardInterrupt:
    print("\n用户中断测试")
except Exception as e:
    print(f"测试异常: {e}")
