#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
绿色色块检测测试脚本
用于验证检测算法的准确性
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from maix import camera, display, image, app, time
    from config import GREEN_THRESHOLD, BLOB_MIN_PIXELS
    
    def test_green_detection():
        """测试绿色检测功能"""
        print("开始测试绿色色块检测...")
        
        # 初始化摄像头和显示屏
        cam = camera.Camera(320, 240)
        disp = display.Display()
        
        test_count = 0
        detection_count = 0
        
        print("测试进行中，按Ctrl+C退出...")
        
        while not app.need_exit() and test_count < 100:  # 测试100帧
            try:
                img = cam.read()
                
                # 检测绿色色块
                blobs = img.find_blobs(GREEN_THRESHOLD, pixels_threshold=BLOB_MIN_PIXELS)
                
                # 绘制检测结果
                for i, blob in enumerate(blobs):
                    img.draw_rect(blob[0], blob[1], blob[2], blob[3], image.COLOR_GREEN, 2)
                    center_x = blob[0] + blob[2] // 2
                    center_y = blob[1] + blob[3] // 2
                    img.draw_circle(center_x, center_y, 3, image.COLOR_RED, 2)
                    
                    # 显示色块信息
                    info = f"色块{i+1}: 大小{blob[2]}x{blob[3]}"
                    img.draw_string(blob[0], blob[1] - 15, info, image.COLOR_WHITE)
                
                # 显示测试信息
                img.draw_string(10, 10, f"测试帧: {test_count+1}/100", image.COLOR_YELLOW)
                img.draw_string(10, 30, f"检测到: {len(blobs)} 个色块", image.COLOR_YELLOW)
                
                if len(blobs) > 0:
                    detection_count += 1
                
                disp.show(img)
                test_count += 1
                time.sleep_ms(50)  # 稍慢一点便于观察
                
            except Exception as e:
                print(f"测试错误: {e}")
                break
        
        # 输出测试结果
        detection_rate = (detection_count / test_count) * 100 if test_count > 0 else 0
        print(f"\n测试完成!")
        print(f"总测试帧数: {test_count}")
        print(f"检测到色块的帧数: {detection_count}")
        print(f"检测率: {detection_rate:.1f}%")
        
        if detection_rate > 50:
            print("✓ 检测性能良好")
        elif detection_rate > 20:
            print("⚠ 检测性能一般，建议调整阈值")
        else:
            print("✗ 检测性能较差，请检查环境光线和阈值设置")
    
    if __name__ == "__main__":
        test_green_detection()
        
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保在MaixCAM设备上运行此测试")
except KeyboardInterrupt:
    print("\n用户中断测试")
except Exception as e:
    print(f"测试异常: {e}")
