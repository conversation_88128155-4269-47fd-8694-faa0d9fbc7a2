# MaixCAM 阈值获取工具使用指南

## 功能概述

阈值获取工具是一个脱机的颜色阈值自动计算系统，允许用户通过简单的触摸操作来获取最佳的颜色检测阈值，无需手动调试参数。

## 界面布局

### 按钮位置

```
┌─────────────────────────────────────┐
│  摄像头画面                         │
│                                     │
│                                     │
│                                     │
│                                     │
│                                     │
│                                     │
│                                     │
│                                     │
│ ┌────────┐           ┌──────────┐   │
│ │  LAB   │           │ 取阈值   │   │ ← 按钮区域
│ └────────┘           └──────────┘   │
└─────────────────────────────────────┘
```

- **左下角**: LAB/二值化切换按钮
- **右下角**: 阈值获取按钮

## 使用流程

### 步骤1: 进入阈值获取模式

1. 触摸屏幕右下角的"取阈值"按钮
2. 按钮变为红色，表示进入阈值获取模式
3. 屏幕显示"阈值模式: 触摸选择区域"

### 步骤2: 选择采样区域

1. 将要检测的色块放在摄像头前
2. 触摸色块所在的区域
3. 系统显示黄色采样矩形和十字线
4. 自动计算该区域的LAB阈值

### 步骤3: 应用新阈值

1. 系统自动应用计算出的阈值
2. 实时显示检测效果
3. 阈值自动保存到本地文件
4. 退出阈值获取模式

## 技术原理

### LAB色彩空间

阈值获取工具使用LAB色彩空间进行颜色分析：

- **L通道**: 亮度 (0-255)
- **A通道**: 绿红色彩 (-128到127)
- **B通道**: 蓝黄色彩 (-128到127)

### 采样策略

```python
# 采样区域: 40x40像素 (可配置)
# 采样方法: 8x8网格均匀采样
# 采样点数: 最多64个点
```

### 阈值计算算法

1. **统计分析**:
   ```
   L_min, L_max = min(L值), max(L值)
   A_min, A_max = min(A值), max(A值)  
   B_min, B_max = min(B值), max(B值)
   ```

2. **边距扩展**:
   ```
   边距 = max(固定边距, 颜色范围/4)
   最终阈值 = [原始范围 ± 边距]
   ```

3. **范围限制**:
   ```
   L: [0, 255]
   A: [-128, 127]
   B: [-128, 127]
   ```

## 配置参数

### 基本配置

```python
# config.py
THRESHOLD_RECT = [220, 200, 90, 30]  # 按钮位置和大小
SAMPLE_RECT_SIZE = 40                # 采样矩形大小
THRESHOLD_SAMPLES = 50               # 最大采样点数
THRESHOLD_MARGIN = 10                # 最小边距
ENABLE_THRESHOLD_TOOL = True         # 启用工具
```

### 参数说明

| 参数 | 说明 | 推荐值 | 调整建议 |
|------|------|--------|----------|
| SAMPLE_RECT_SIZE | 采样区域大小 | 40 | 大区域更稳定，小区域更精确 |
| THRESHOLD_MARGIN | 阈值边距 | 10 | 增大提高检测范围，减小提高精度 |
| THRESHOLD_SAMPLES | 采样点数 | 50 | 更多采样点提高准确性 |

## 最佳实践

### 采样技巧

1. **选择合适的区域**:
   - 颜色均匀的区域
   - 避免边缘和阴影
   - 确保光照充足

2. **多次采样**:
   - 在不同光照条件下采样
   - 对同一颜色的不同区域采样
   - 比较不同阈值的效果

3. **验证效果**:
   - 切换到二值化模式查看效果
   - 测试不同距离和角度
   - 确保检测稳定性

### 光照条件

| 光照类型 | 适用性 | 注意事项 |
|----------|--------|----------|
| 自然光 | 最佳 | 避免强烈阴影 |
| 室内灯光 | 良好 | 注意色温影响 |
| LED灯 | 一般 | 可能有频闪影响 |
| 强光/背光 | 不推荐 | 会影响颜色准确性 |

## 阈值管理

### 自动保存

每次成功获取阈值后，系统自动保存：

```json
{
  "auto_1234567890": {
    "threshold": [[L_min, L_max, A_min, A_max, B_min, B_max]],
    "description": "自动获取的白色阈值",
    "color_type": "WHITE",
    "created_time": "2024-01-01 12:00:00",
    "usage_count": 0
  }
}
```

### 手动管理

使用阈值管理工具：

```bash
python threshold_manager.py
```

常用命令：
- `list` - 查看所有阈值
- `info auto_1234567890` - 查看阈值详情
- `export auto_1234567890 WHITE` - 导出到配置文件
- `delete auto_1234567890` - 删除阈值

## 故障排除

### 常见问题

1. **阈值计算失败**
   ```
   原因: 采样区域像素点太少
   解决: 选择更大的均匀区域
   ```

2. **检测效果不佳**
   ```
   原因: 光照条件变化或颜色不均匀
   解决: 重新在当前条件下获取阈值
   ```

3. **按钮无响应**
   ```
   原因: 触摸位置不准确
   解决: 确保触摸在按钮区域内
   ```

4. **阈值过于宽泛**
   ```
   原因: THRESHOLD_MARGIN设置过大
   解决: 减小边距参数
   ```

### 调试方法

#### 查看采样信息
```python
# 在calculate_threshold_from_region函数中添加调试输出
print(f"采样点数: {len(l_values)}")
print(f"L范围: {l_min}-{l_max}")
print(f"A范围: {a_min}-{a_max}")
print(f"B范围: {b_min}-{b_max}")
```

#### 测试阈值效果
```bash
python test_threshold_tool.py
```

## 高级功能

### 批量阈值获取

可以为不同的颜色或光照条件获取多个阈值：

1. 获取室内光线下的阈值
2. 获取室外光线下的阈值
3. 获取不同角度的阈值
4. 比较和选择最佳阈值

### 阈值融合

将多个阈值合并为更鲁棒的阈值：

```python
# 示例：合并两个阈值
threshold1 = [[20, 80, -30, 10, -20, 30]]
threshold2 = [[25, 85, -25, 15, -15, 35]]

# 取并集
merged = [[
    min(20, 25), max(80, 85),  # L范围
    min(-30, -25), max(10, 15),  # A范围  
    min(-20, -15), max(30, 35)   # B范围
]]
```

### 环境自适应

根据环境光线自动调整阈值：

1. 检测当前光照强度
2. 选择对应的阈值配置
3. 动态切换阈值参数

## 性能优化

### 采样优化

- 减少采样点数以提高速度
- 使用固定步长而非密集采样
- 缓存计算结果避免重复计算

### 内存管理

- 及时释放临时图像
- 限制保存的阈值数量
- 定期清理过期阈值

## 扩展应用

### 多颜色支持

扩展支持更多颜色：

```python
# 添加新颜色配置
RED_THRESHOLD = [[...]]
BLUE_THRESHOLD = [[...]]
YELLOW_THRESHOLD = [[...]]
```

### 形状检测

结合形状特征进行检测：

```python
# 添加形状约束
MIN_AREA = 100
MAX_AREA = 5000
MIN_ASPECT_RATIO = 0.5
MAX_ASPECT_RATIO = 2.0
```

### 机器学习集成

使用机器学习优化阈值选择：

1. 收集大量阈值样本
2. 训练阈值预测模型
3. 根据图像特征自动推荐阈值
