#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
触摸按钮功能测试脚本
用于测试触摸屏矩形按钮的响应和功能
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from maix import touchscreen, display, image, app, time
    from config import BUTTON_RECT, BUTTON_DEBOUNCE_MS, ENABLE_TOUCH_BUTTON
    
    def is_point_in_rect(x, y, rect):
        """检查点是否在矩形内"""
        rx, ry, rw, rh = rect
        return rx <= x <= rx + rw and ry <= y <= ry + rh
    
    def test_touch_button():
        """测试触摸按钮功能"""
        print("开始测试触摸按钮功能...")
        print(f"按钮区域: {BUTTON_RECT} [x, y, width, height]")
        print(f"防抖时间: {BUTTON_DEBOUNCE_MS}ms")
        
        if not ENABLE_TOUCH_BUTTON:
            print("触摸按钮功能已禁用，请在config.py中启用")
            return False
        
        try:
            # 初始化触摸屏和显示屏
            ts = touchscreen.TouchScreen()
            disp = display.Display()
            
            # 测试变量
            button_touch_count = 0
            other_touch_count = 0
            last_button_time = 0
            lab_mode = False
            
            print("触摸按钮测试开始，按Ctrl+C退出...")
            print("请触摸屏幕上的按钮区域进行测试")
            
            while not app.need_exit():
                current_time = time.ticks_ms()
                x, y, pressed = ts.read()
                
                if pressed:
                    # 检查是否触摸了按钮区域
                    if is_point_in_rect(x, y, BUTTON_RECT):
                        if (current_time - last_button_time) > BUTTON_DEBOUNCE_MS:
                            button_touch_count += 1
                            lab_mode = not lab_mode
                            last_button_time = current_time
                            print(f"按钮触摸 #{button_touch_count} - 模式: {'LAB' if lab_mode else 'Binary'}")
                    else:
                        other_touch_count += 1
                        print(f"其他区域触摸 #{other_touch_count} - 坐标: ({x}, {y})")
                
                # 创建测试图像
                img = image.Image(disp.width(), disp.height())
                img.draw_rect(0, 0, img.width(), img.height(), image.COLOR_BLACK, -1)
                
                # 绘制按钮区域
                bx, by, bw, bh = BUTTON_RECT
                button_color = image.COLOR_BLUE if lab_mode else image.COLOR_GRAY
                text_color = image.COLOR_WHITE if lab_mode else image.COLOR_BLACK
                
                # 绘制按钮
                img.draw_rect(bx, by, bw, bh, button_color, -1)
                img.draw_rect(bx, by, bw, bh, image.COLOR_WHITE, 2)
                
                # 绘制按钮文字
                button_text = "LAB" if lab_mode else "Binary"
                text_x = bx + (bw - len(button_text) * 6) // 2
                text_y = by + (bh - 12) // 2
                img.draw_string(text_x, text_y, button_text, text_color)
                
                # 显示测试信息
                y_pos = 20
                test_texts = [
                    f"Touch Button Test",
                    f"Button Area: {BUTTON_RECT}",
                    f"Button Touches: {button_touch_count}",
                    f"Other Touches: {other_touch_count}",
                    f"Current Mode: {'LAB' if lab_mode else 'Binary'}",
                    f"Touch Position: ({x}, {y})" if pressed else "No Touch",
                    "Touch button area to test"
                ]
                
                for text in test_texts:
                    color = image.COLOR_GREEN if "Current Mode" in text else image.COLOR_WHITE
                    img.draw_string(120, y_pos, text, color)
                    y_pos += 20
                
                # 显示触摸状态指示器
                if pressed:
                    if is_point_in_rect(x, y, BUTTON_RECT):
                        # 按钮区域触摸
                        img.draw_circle(x, y, 5, image.COLOR_RED, -1)
                        img.draw_string(x + 10, y - 5, "BUTTON", image.COLOR_RED)
                    else:
                        # 其他区域触摸
                        img.draw_circle(x, y, 5, image.COLOR_YELLOW, -1)
                        img.draw_string(x + 10, y - 5, "OTHER", image.COLOR_YELLOW)
                
                disp.show(img)
                time.sleep_ms(10)  # 短暂延时
                
            print(f"\n测试完成!")
            print(f"按钮触摸次数: {button_touch_count}")
            print(f"其他区域触摸次数: {other_touch_count}")
            return True
            
        except Exception as e:
            print(f"✗ 触摸按钮测试失败: {e}")
            return False
    
    def test_touch_coordinates():
        """测试触摸坐标显示"""
        print("触摸坐标测试...")
        
        try:
            ts = touchscreen.TouchScreen()
            disp = display.Display()
            
            print("触摸屏幕查看坐标，按Ctrl+C退出")
            
            touch_history = []
            
            while not app.need_exit():
                x, y, pressed = ts.read()
                
                if pressed:
                    touch_history.append((x, y))
                    if len(touch_history) > 10:  # 保留最近10个触摸点
                        touch_history.pop(0)
                    print(f"触摸坐标: ({x}, {y})")
                
                # 创建测试图像
                img = image.Image(disp.width(), disp.height())
                img.draw_rect(0, 0, img.width(), img.height(), image.COLOR_BLACK, -1)
                
                # 绘制按钮区域参考
                bx, by, bw, bh = BUTTON_RECT
                img.draw_rect(bx, by, bw, bh, image.COLOR_GRAY, 2)
                img.draw_string(bx, by - 15, "Button Area", image.COLOR_WHITE)
                
                # 显示当前触摸点
                if pressed:
                    img.draw_circle(x, y, 8, image.COLOR_RED, -1)
                    img.draw_string(x + 15, y - 5, f"({x},{y})", image.COLOR_WHITE)
                
                # 显示历史触摸点
                for i, (hx, hy) in enumerate(touch_history):
                    alpha = 255 - i * 20  # 渐变效果
                    if alpha > 0:
                        img.draw_circle(hx, hy, 3, image.COLOR_YELLOW, -1)
                
                # 显示信息
                img.draw_string(10, 10, "Touch Coordinate Test", image.COLOR_WHITE)
                img.draw_string(10, 30, f"Screen: {disp.width()}x{disp.height()}", image.COLOR_WHITE)
                img.draw_string(10, 50, f"Button: {BUTTON_RECT}", image.COLOR_WHITE)
                
                disp.show(img)
                time.sleep_ms(50)
                
        except Exception as e:
            print(f"坐标测试失败: {e}")
    
    def main():
        """主函数"""
        print("=== MaixCAM 触摸按钮功能测试 ===")
        print()
        
        if len(sys.argv) > 1 and sys.argv[1] == "coord":
            test_touch_coordinates()
        else:
            test_touch_button()
    
    if __name__ == "__main__":
        main()
        
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保在MaixCAM设备上运行此测试")
except KeyboardInterrupt:
    print("\n用户中断测试")
except Exception as e:
    print(f"测试异常: {e}")
