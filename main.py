#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MaixCAM 绿色色块识别与触摸屏二值化切换程序
功能：1.识别绿色色块 2.触摸屏切换二值化图像 3.支持maixcam摄像头
"""

from maix import camera, display, image, touchscreen, app, time
try:
    from config import *  # 导入配置参数
except ImportError:
    # 如果没有config.py文件，使用默认配置
    CAMERA_WIDTH = 320
    CAMERA_HEIGHT = 240
    GREEN_THRESHOLD = [[0, 80, -120, -10, 0, 30]]
    WHITE_THRESHOLD = [[80, 100, -10, 10, -10, 10]]
    CURRENT_COLOR = "WHITE"
    BLOB_MIN_PIXELS = 500
    TOUCH_DEBOUNCE_MS = 200
    FRAME_DELAY_MS = 10
    BUTTON_RECT = [10, 200, 80, 30]
    BUTTON_DEBOUNCE_MS = 200
    ENABLE_TOUCH_BUTTON = True
    FONT_PATHS = [
        "/maixapp/share/font/SourceHanSansCN-Regular.otf",
        "/maixapp/share/font/ppocr_keys_v1.ttf"
    ]
    FONT_SIZE = 16
    FORCE_ENGLISH = False

class ColorBlobDetector:
    """色块检测器（支持绿色和白色）"""

    def __init__(self):
        self.cam = camera.Camera(CAMERA_WIDTH, CAMERA_HEIGHT)  # 初始化摄像头
        self.disp = display.Display()  # 初始化显示屏
        self.ts = touchscreen.TouchScreen()  # 初始化触摸屏
        self.binary_mode = False  # 二值化模式标志
        self.lab_mode = False  # LAB模式标志
        self.last_touch_time = 0  # 上次触摸时间
        self.last_button_time = 0  # 上次按钮触摸时间
        self.font_loaded = False  # 字体加载状态
        self.use_chinese = False  # 是否使用中文
        self.current_color = CURRENT_COLOR  # 当前检测颜色

        # 尝试加载中文字体
        self._load_font()

    def _load_font(self):
        """加载中文字体"""
        try:
            # 检查是否强制使用英文
            if FORCE_ENGLISH:
                print("配置强制使用英文显示")
                self.use_chinese = False
                return

            # 尝试加载配置中的字体路径
            for font_path in FONT_PATHS:
                try:
                    image.load_font("chinese", font_path, size=FONT_SIZE)
                    image.set_default_font("chinese")
                    self.font_loaded = True
                    self.use_chinese = True
                    print(f"成功加载字体: {font_path}")
                    break
                except:
                    continue

            if not self.font_loaded:
                print("警告: 无法加载中文字体，将使用英文显示")
                print("提示: 运行 'python install_font.py' 检查字体安装")
                self.use_chinese = False

        except Exception as e:
            print(f"字体加载错误: {e}")
            self.use_chinese = False

    def get_text(self, key):
        """获取显示文本（中英文切换）"""
        color_name_cn = "绿色" if self.current_color == "GREEN" else "白色"
        color_name_en = "green" if self.current_color == "GREEN" else "white"

        texts = {
            "mode_color": "模式: 彩色" if self.use_chinese else "Mode: Color",
            "mode_binary": "模式: 二值化" if self.use_chinese else "Mode: Binary",
            "mode_lab": "模式: LAB" if self.use_chinese else "Mode: LAB",
            "touch_tip": "触摸屏幕切换模式" if self.use_chinese else "Touch to switch",
            "button_tip": "按钮切换LAB/二值化" if self.use_chinese else "Button: LAB/Binary",
            "detected": f"检测到 {{}} 个{color_name_cn}色块" if self.use_chinese else f"Found {{}} {color_name_en} blobs",
            "color_blob": f"{color_name_cn}块{{}}: ({{}},{{}})" if self.use_chinese else f"{color_name_en.title()} blob{{}}: ({{}},{{}})",
            "current_color": f"检测颜色: {color_name_cn}" if self.use_chinese else f"Detecting: {color_name_en}",
            "lab_btn": "LAB" if self.lab_mode else ("二值化" if self.use_chinese else "Binary")
        }
        return texts.get(key, key)

    def detect_color_blobs(self, img):
        """检测指定颜色的色块"""
        if self.current_color == "GREEN":
            threshold = GREEN_THRESHOLD
        else:  # WHITE
            threshold = WHITE_THRESHOLD

        blobs = img.find_blobs(threshold, pixels_threshold=BLOB_MIN_PIXELS)
        return blobs

    def draw_blob_info(self, img, blobs):
        """绘制色块信息"""
        # 根据检测颜色选择边框颜色
        border_color = image.COLOR_GREEN if self.current_color == "GREEN" else image.COLOR_BLUE

        for i, blob in enumerate(blobs):
            # 绘制彩色矩形框
            img.draw_rect(blob[0], blob[1], blob[2], blob[3], border_color, 2)
            # 绘制中心点
            center_x = blob[0] + blob[2] // 2
            center_y = blob[1] + blob[3] // 2
            img.draw_circle(center_x, center_y, 3, image.COLOR_RED, 2)
            # 显示色块信息
            info_text = self.get_text("color_blob").format(i+1, center_x, center_y)
            text_color = image.COLOR_BLACK if self.current_color == "WHITE" else image.COLOR_WHITE
            img.draw_string(blob[0], blob[1] - 15, info_text, text_color)

    def is_point_in_rect(self, x, y, rect):
        """检查点是否在矩形内"""
        rx, ry, rw, rh = rect
        return rx <= x <= rx + rw and ry <= y <= ry + rh

    def handle_touch(self):
        """处理触摸事件"""
        current_time = time.ticks_ms()
        x, y, pressed = self.ts.read()

        if pressed:
            # 检查是否触摸了LAB/二值化切换按钮
            if (ENABLE_TOUCH_BUTTON and
                self.is_point_in_rect(x, y, BUTTON_RECT) and
                (current_time - self.last_button_time) > BUTTON_DEBOUNCE_MS):

                self.lab_mode = not self.lab_mode  # 切换LAB模式
                self.last_button_time = current_time
                mode_text = "LAB" if self.lab_mode else ("二值化" if self.use_chinese else "Binary")
                print(f"Button touch: {mode_text} mode")

            # 检查是否触摸了其他区域（切换彩色/二值化）
            elif (not ENABLE_TOUCH_BUTTON or
                  not self.is_point_in_rect(x, y, BUTTON_RECT)) and \
                 (current_time - self.last_touch_time) > TOUCH_DEBOUNCE_MS:

                self.binary_mode = not self.binary_mode  # 切换二值化模式
                self.lab_mode = False  # 退出LAB模式
                self.last_touch_time = current_time
                mode_text = "开启" if self.binary_mode else "关闭"
                if not self.use_chinese:
                    mode_text = "ON" if self.binary_mode else "OFF"
                print(f"Touch switch: Binary mode {mode_text}")

    def draw_touch_button(self, img):
        """绘制触摸按钮"""
        if not ENABLE_TOUCH_BUTTON:
            return

        x, y, w, h = BUTTON_RECT

        # 根据当前模式选择按钮颜色
        if self.lab_mode:
            button_color = image.COLOR_BLUE
            text_color = image.COLOR_WHITE
        else:
            button_color = image.COLOR_GRAY
            text_color = image.COLOR_BLACK

        # 绘制按钮背景
        img.draw_rect(x, y, w, h, button_color, -1)
        # 绘制按钮边框
        img.draw_rect(x, y, w, h, image.COLOR_WHITE, 2)

        # 绘制按钮文字
        button_text = self.get_text("lab_btn")
        text_x = x + (w - len(button_text) * 6) // 2  # 居中显示
        text_y = y + (h - 12) // 2
        img.draw_string(text_x, text_y, button_text, text_color)

    def process_image(self, img):
        """图像处理"""
        if self.lab_mode:
            # LAB模式处理
            lab_img = img.copy()
            blobs = self.detect_color_blobs(lab_img)

            # 转换为LAB色彩空间显示
            lab_img = lab_img.to_format(image.Format.FMT_LAB888)
            lab_img = lab_img.to_format(image.Format.FMT_RGB888)  # 转回RGB用于显示

            # 在LAB图像上绘制检测结果
            self.draw_blob_info(lab_img, blobs)

            # 显示模式信息
            lab_img.draw_string(10, 10, self.get_text("mode_lab"), image.COLOR_YELLOW)
            lab_img.draw_string(10, 30, self.get_text("touch_tip"), image.COLOR_YELLOW)
            lab_img.draw_string(10, 50, self.get_text("button_tip"), image.COLOR_YELLOW)
            lab_img.draw_string(10, 70, self.get_text("current_color"), image.COLOR_YELLOW)

            # 绘制触摸按钮
            self.draw_touch_button(lab_img)

            return lab_img
        elif self.binary_mode:
            # 二值化处理
            binary_img = img.copy()
            blobs = self.detect_color_blobs(binary_img)

            # 根据当前检测颜色选择阈值
            threshold = GREEN_THRESHOLD if self.current_color == "GREEN" else WHITE_THRESHOLD

            # 创建二值化图像
            binary_img = binary_img.to_format(image.Format.FMT_GRAYSCALE)
            binary_img = binary_img.binary(threshold, invert=False)
            binary_img = binary_img.to_format(image.Format.FMT_RGB888)

            # 在二值化图像上绘制检测结果
            self.draw_blob_info(binary_img, blobs)

            # 显示模式信息
            binary_img.draw_string(10, 10, self.get_text("mode_binary"), image.COLOR_YELLOW)
            binary_img.draw_string(10, 30, self.get_text("touch_tip"), image.COLOR_YELLOW)
            binary_img.draw_string(10, 50, self.get_text("button_tip"), image.COLOR_YELLOW)
            binary_img.draw_string(10, 70, self.get_text("current_color"), image.COLOR_YELLOW)

            # 绘制触摸按钮
            self.draw_touch_button(binary_img)

            return binary_img
        else:
            # 正常彩色模式
            blobs = self.detect_color_blobs(img)
            self.draw_blob_info(img, blobs)

            # 显示模式信息
            img.draw_string(10, 10, self.get_text("mode_color"), image.COLOR_YELLOW)
            img.draw_string(10, 30, self.get_text("touch_tip"), image.COLOR_YELLOW)
            img.draw_string(10, 50, self.get_text("button_tip"), image.COLOR_YELLOW)
            img.draw_string(10, 70, self.get_text("detected").format(len(blobs)), image.COLOR_YELLOW)
            img.draw_string(10, 90, self.get_text("current_color"), image.COLOR_YELLOW)

            # 绘制触摸按钮
            self.draw_touch_button(img)

            return img

    def run(self):
        """主运行循环"""
        color_name = "白色" if self.current_color == "WHITE" else "绿色"
        color_name_en = "white" if self.current_color == "WHITE" else "green"

        if self.use_chinese:
            print(f"{color_name}色块检测程序启动...")
            print("触摸屏幕可切换彩色/二值化模式")
            if ENABLE_TOUCH_BUTTON:
                print("触摸按钮可切换LAB/二值化显示模式")
        else:
            print(f"{color_name_en.title()} blob detection started...")
            print("Touch screen to switch color/binary mode")
            if ENABLE_TOUCH_BUTTON:
                print("Touch button to switch LAB/Binary display mode")

        while not app.need_exit():
            try:
                # 读取摄像头图像
                img = self.cam.read()

                # 处理触摸事件
                self.handle_touch()

                # 处理图像
                processed_img = self.process_image(img)

                # 显示图像
                self.disp.show(processed_img)

                # 短暂延时以降低CPU使用率
                time.sleep_ms(FRAME_DELAY_MS)

            except Exception as e:
                error_msg = f"运行错误: {e}" if self.use_chinese else f"Runtime error: {e}"
                print(error_msg)
                time.sleep_ms(100)

        exit_msg = "程序退出" if self.use_chinese else "Program exit"
        print(exit_msg)

def main():
    """主函数"""
    try:
        detector = ColorBlobDetector()
        detector.run()
    except KeyboardInterrupt:
        print("User interrupted" if not hasattr(detector, 'use_chinese') or not detector.use_chinese else "用户中断程序")
    except Exception as e:
        print(f"Program exception: {e}")

if __name__ == "__main__":
    main()