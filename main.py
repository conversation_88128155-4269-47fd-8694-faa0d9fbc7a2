#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MaixCAM 绿色色块识别与触摸屏二值化切换程序
功能：1.识别绿色色块 2.触摸屏切换二值化图像 3.支持maixcam摄像头
"""

from maix import camera, display, image, touchscreen, app, time
try:
    from config import *  # 导入配置参数
except ImportError:
    # 如果没有config.py文件，使用默认配置
    CAMERA_WIDTH = 320
    CAMERA_HEIGHT = 240
    GREEN_THRESHOLD = [[0, 80, -120, -10, 0, 30]]
    BLOB_MIN_PIXELS = 500
    TOUCH_DEBOUNCE_MS = 200
    FRAME_DELAY_MS = 10

class GreenBlobDetector:
    """绿色色块检测器"""

    def __init__(self):
        self.cam = camera.Camera(CAMERA_WIDTH, CAMERA_HEIGHT)  # 初始化摄像头
        self.disp = display.Display()  # 初始化显示屏
        self.ts = touchscreen.TouchScreen()  # 初始化触摸屏
        self.binary_mode = False  # 二值化模式标志
        self.last_touch_time = 0  # 上次触摸时间

    def detect_green_blobs(self, img):
        """检测绿色色块"""
        blobs = img.find_blobs(GREEN_THRESHOLD, pixels_threshold=BLOB_MIN_PIXELS)
        return blobs

    def draw_blob_info(self, img, blobs):
        """绘制色块信息"""
        for i, blob in enumerate(blobs):
            # 绘制绿色矩形框
            img.draw_rect(blob[0], blob[1], blob[2], blob[3], image.COLOR_GREEN, 2)
            # 绘制中心点
            center_x = blob[0] + blob[2] // 2
            center_y = blob[1] + blob[3] // 2
            img.draw_circle(center_x, center_y, 3, image.COLOR_RED, 2)
            # 显示色块信息
            info_text = f"绿色块{i+1}: ({center_x},{center_y})"
            img.draw_string(blob[0], blob[1] - 15, info_text, image.COLOR_WHITE)

    def handle_touch(self):
        """处理触摸事件"""
        current_time = time.ticks_ms()
        _, _, pressed = self.ts.read()  # 只需要pressed状态，忽略坐标

        # 触摸防抖处理
        if pressed and (current_time - self.last_touch_time) > TOUCH_DEBOUNCE_MS:
            self.binary_mode = not self.binary_mode  # 切换二值化模式
            self.last_touch_time = current_time
            print(f"触摸切换: 二值化模式 {'开启' if self.binary_mode else '关闭'}")

    def process_image(self, img):
        """图像处理"""
        if self.binary_mode:
            # 二值化处理
            binary_img = img.copy()
            blobs = self.detect_green_blobs(binary_img)

            # 创建二值化图像
            binary_img = binary_img.to_format(image.Format.FMT_GRAYSCALE)
            binary_img = binary_img.binary(GREEN_THRESHOLD, invert=False)
            binary_img = binary_img.to_format(image.Format.FMT_RGB888)

            # 在二值化图像上绘制检测结果
            self.draw_blob_info(binary_img, blobs)

            # 显示模式信息
            binary_img.draw_string(10, 10, "模式: 二值化", image.COLOR_YELLOW)
            binary_img.draw_string(10, 30, "触摸屏幕切换模式", image.COLOR_YELLOW)

            return binary_img
        else:
            # 正常彩色模式
            blobs = self.detect_green_blobs(img)
            self.draw_blob_info(img, blobs)

            # 显示模式信息
            img.draw_string(10, 10, "模式: 彩色", image.COLOR_YELLOW)
            img.draw_string(10, 30, "触摸屏幕切换模式", image.COLOR_YELLOW)
            img.draw_string(10, 50, f"检测到 {len(blobs)} 个绿色色块", image.COLOR_YELLOW)

            return img

    def run(self):
        """主运行循环"""
        print("绿色色块检测程序启动...")
        print("触摸屏幕可切换彩色/二值化模式")

        while not app.need_exit():
            try:
                # 读取摄像头图像
                img = self.cam.read()

                # 处理触摸事件
                self.handle_touch()

                # 处理图像
                processed_img = self.process_image(img)

                # 显示图像
                self.disp.show(processed_img)

                # 短暂延时以降低CPU使用率
                time.sleep_ms(FRAME_DELAY_MS)

            except Exception as e:
                print(f"运行错误: {e}")
                time.sleep_ms(100)

        print("程序退出")

def main():
    """主函数"""
    try:
        detector = GreenBlobDetector()
        detector.run()
    except KeyboardInterrupt:
        print("用户中断程序")
    except Exception as e:
        print(f"程序异常: {e}")

if __name__ == "__main__":
    main()