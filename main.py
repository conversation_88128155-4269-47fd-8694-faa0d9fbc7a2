#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MaixCAM 绿色色块识别与触摸屏二值化切换程序
功能：1.识别绿色色块 2.触摸屏切换二值化图像 3.支持maixcam摄像头
"""

from maix import camera, display, image, touchscreen, app, time
try:
    from threshold_manager import ThresholdManager
except ImportError:
    ThresholdManager = None
try:
    from config import *  # 导入配置参数
except ImportError:
    # 如果没有config.py文件，使用默认配置
    CAMERA_WIDTH = 320
    CAMERA_HEIGHT = 240
    GREEN_THRESHOLD = [[0, 80, -120, -10, 0, 30]]
    WHITE_THRESHOLD = [[80, 100, -10, 10, -10, 10]]
    CURRENT_COLOR = "WHITE"
    BLOB_MIN_PIXELS = 500
    TOUCH_DEBOUNCE_MS = 200
    FRAME_DELAY_MS = 10
    BUTTON_RECT = [10, 200, 80, 30]
    BUTTON_DEBOUNCE_MS = 200
    ENABLE_TOUCH_BUTTON = True
    THRESHOLD_RECT = [220, 200, 90, 30]
    SAMPLE_RECT_SIZE = 40
    THRESHOLD_SAMPLES = 50
    THRESHOLD_MARGIN = 10
    ENABLE_THRESHOLD_TOOL = True
    FONT_PATHS = [
        "/maixapp/share/font/SourceHanSansCN-Regular.otf",
        "/maixapp/share/font/ppocr_keys_v1.ttf"
    ]
    FONT_SIZE = 16
    FORCE_ENGLISH = False

class ColorBlobDetector:
    """色块检测器（支持绿色和白色）"""

    def __init__(self):
        self.cam = camera.Camera(CAMERA_WIDTH, CAMERA_HEIGHT)  # 初始化摄像头
        self.disp = display.Display()  # 初始化显示屏
        self.ts = touchscreen.TouchScreen()  # 初始化触摸屏
        self.binary_mode = False  # 二值化模式标志
        self.lab_mode = False  # LAB模式标志
        self.threshold_mode = False  # 阈值获取模式标志
        self.sample_rect = None  # 采样矩形位置
        self.last_touch_time = 0  # 上次触摸时间
        self.last_button_time = 0  # 上次按钮触摸时间
        self.last_threshold_time = 0  # 上次阈值按钮触摸时间
        self.font_loaded = False  # 字体加载状态
        self.use_chinese = False  # 是否使用中文
        self.current_color = CURRENT_COLOR  # 当前检测颜色
        self.custom_threshold = None  # 自定义阈值

        # 初始化阈值管理器
        if ThresholdManager:
            self.threshold_manager = ThresholdManager()
        else:
            self.threshold_manager = None

        # 尝试加载中文字体
        self._load_font()

    def _load_font(self):
        """加载中文字体"""
        try:
            # 检查是否强制使用英文
            if FORCE_ENGLISH:
                print("配置强制使用英文显示")
                self.use_chinese = False
                return

            # 尝试加载配置中的字体路径
            for font_path in FONT_PATHS:
                try:
                    image.load_font("chinese", font_path, size=FONT_SIZE)
                    image.set_default_font("chinese")
                    self.font_loaded = True
                    self.use_chinese = True
                    print(f"成功加载字体: {font_path}")
                    break
                except:
                    continue

            if not self.font_loaded:
                print("警告: 无法加载中文字体，将使用英文显示")
                print("提示: 运行 'python install_font.py' 检查字体安装")
                self.use_chinese = False

        except Exception as e:
            print(f"字体加载错误: {e}")
            self.use_chinese = False

    def get_text(self, key):
        """获取显示文本（中英文切换）"""
        color_name_cn = "绿色" if self.current_color == "GREEN" else "白色"
        color_name_en = "green" if self.current_color == "GREEN" else "white"

        texts = {
            "mode_color": "模式: 彩色" if self.use_chinese else "Mode: Color",
            "mode_binary": "模式: 二值化" if self.use_chinese else "Mode: Binary",
            "mode_lab": "模式: LAB" if self.use_chinese else "Mode: LAB",
            "touch_tip": "触摸屏幕切换模式" if self.use_chinese else "Touch to switch",
            "button_tip": "按钮切换LAB/二值化" if self.use_chinese else "Button: LAB/Binary",
            "threshold_tip": "取阈值按钮" if self.use_chinese else "Get Threshold",
            "detected": f"检测到 {{}} 个{color_name_cn}色块" if self.use_chinese else f"Found {{}} {color_name_en} blobs",
            "color_blob": f"{color_name_cn}块{{}}: ({{}},{{}})" if self.use_chinese else f"{color_name_en.title()} blob{{}}: ({{}},{{}})",
            "current_color": f"检测颜色: {color_name_cn}" if self.use_chinese else f"Detecting: {color_name_en}",
            "lab_btn": "LAB" if self.lab_mode else ("二值化" if self.use_chinese else "Binary"),
            "threshold_btn": "取阈值" if self.use_chinese else "Get Threshold",
            "threshold_mode": "阈值模式: 触摸选择区域" if self.use_chinese else "Threshold Mode: Touch to select area",
            "threshold_result": "阈值已更新" if self.use_chinese else "Threshold Updated"
        }
        return texts.get(key, key)

    def calculate_threshold_from_region(self, img, x, y, size):
        """从指定区域计算LAB阈值"""
        try:
            # 确保采样区域在图像范围内
            x1 = max(0, x - size // 2)
            y1 = max(0, y - size // 2)
            x2 = min(img.width(), x + size // 2)
            y2 = min(img.height(), y + size // 2)

            # 转换为LAB色彩空间
            lab_img = img.to_format(image.Format.FMT_LAB888)

            # 采样区域内的像素
            l_values = []
            a_values = []
            b_values = []

            # 采样策略：在区域内均匀采样
            step = max(1, (x2 - x1) // 8)  # 8x8网格采样
            for py in range(y1, y2, step):
                for px in range(x1, x2, step):
                    try:
                        # 获取LAB值
                        pixel = lab_img.get_pixel(px, py)
                        l_values.append(pixel[0])
                        a_values.append(pixel[1])
                        b_values.append(pixel[2])
                    except:
                        continue

            if len(l_values) < 10:  # 采样点太少
                return None

            # 计算统计值
            l_min, l_max = min(l_values), max(l_values)
            a_min, a_max = min(a_values), max(a_values)
            b_min, b_max = min(b_values), max(b_values)

            # 添加边距以提高检测稳定性
            l_range = l_max - l_min
            a_range = a_max - a_min
            b_range = b_max - b_min

            l_margin = max(THRESHOLD_MARGIN, l_range // 4)
            a_margin = max(THRESHOLD_MARGIN, a_range // 4)
            b_margin = max(THRESHOLD_MARGIN, b_range // 4)

            # 生成阈值
            threshold = [[
                max(0, l_min - l_margin), min(255, l_max + l_margin),
                max(-128, a_min - a_margin), min(127, a_max + a_margin),
                max(-128, b_min - b_margin), min(127, b_max + b_margin)
            ]]

            return threshold

        except Exception as e:
            print(f"阈值计算错误: {e}")
            return None

    def detect_color_blobs(self, img):
        """检测指定颜色的色块"""
        # 使用自定义阈值或默认阈值
        if self.custom_threshold:
            threshold = self.custom_threshold
        elif self.current_color == "GREEN":
            threshold = GREEN_THRESHOLD
        else:  # WHITE
            threshold = WHITE_THRESHOLD

        blobs = img.find_blobs(threshold, pixels_threshold=BLOB_MIN_PIXELS)
        return blobs

    def draw_blob_info(self, img, blobs):
        """绘制色块信息"""
        # 根据检测颜色选择边框颜色
        border_color = image.COLOR_GREEN if self.current_color == "GREEN" else image.COLOR_BLUE

        for i, blob in enumerate(blobs):
            # 绘制彩色矩形框
            img.draw_rect(blob[0], blob[1], blob[2], blob[3], border_color, 2)
            # 绘制中心点
            center_x = blob[0] + blob[2] // 2
            center_y = blob[1] + blob[3] // 2
            img.draw_circle(center_x, center_y, 3, image.COLOR_RED, 2)
            # 显示色块信息
            info_text = self.get_text("color_blob").format(i+1, center_x, center_y)
            text_color = image.COLOR_BLACK if self.current_color == "WHITE" else image.COLOR_WHITE
            img.draw_string(blob[0], blob[1] - 15, info_text, text_color)

    def is_point_in_rect(self, x, y, rect):
        """检查点是否在矩形内"""
        rx, ry, rw, rh = rect
        return rx <= x <= rx + rw and ry <= y <= ry + rh

    def handle_touch(self):
        """处理触摸事件"""
        current_time = time.ticks_ms()
        x, y, pressed = self.ts.read()

        if pressed:
            # 检查是否触摸了阈值获取按钮
            if (ENABLE_THRESHOLD_TOOL and
                self.is_point_in_rect(x, y, THRESHOLD_RECT) and
                (current_time - self.last_threshold_time) > BUTTON_DEBOUNCE_MS):

                self.threshold_mode = not self.threshold_mode  # 切换阈值获取模式
                self.last_threshold_time = current_time
                if self.threshold_mode:
                    self.sample_rect = None  # 重置采样区域
                    print("进入阈值获取模式，请触摸要采样的区域")
                else:
                    print("退出阈值获取模式")

            # 检查是否触摸了LAB/二值化切换按钮
            elif (ENABLE_TOUCH_BUTTON and
                  self.is_point_in_rect(x, y, BUTTON_RECT) and
                  (current_time - self.last_button_time) > BUTTON_DEBOUNCE_MS):

                self.lab_mode = not self.lab_mode  # 切换LAB模式
                self.last_button_time = current_time
                mode_text = "LAB" if self.lab_mode else ("二值化" if self.use_chinese else "Binary")
                print(f"Button touch: {mode_text} mode")

            # 阈值获取模式下的区域选择
            elif self.threshold_mode:
                # 设置采样矩形位置
                self.sample_rect = [x, y]
                print(f"选择采样区域: ({x}, {y})")

                # 获取当前图像并计算阈值
                try:
                    img = self.cam.read()
                    new_threshold = self.calculate_threshold_from_region(img, x, y, SAMPLE_RECT_SIZE)
                    if new_threshold:
                        self.custom_threshold = new_threshold
                        print(f"新阈值: {new_threshold[0]}")

                        # 自动保存阈值
                        if self.threshold_manager:
                            timestamp = time.ticks_ms()
                            threshold_name = f"auto_{timestamp}"
                            color_name = "绿色" if self.current_color == "GREEN" else "白色"
                            description = f"自动获取的{color_name}阈值"
                            self.threshold_manager.add_threshold(
                                threshold_name, new_threshold, description, self.current_color
                            )
                            print(f"阈值已保存为: {threshold_name}")

                        print("阈值已更新，退出阈值获取模式")
                        self.threshold_mode = False
                        self.sample_rect = None
                    else:
                        print("阈值计算失败，请重新选择区域")
                except Exception as e:
                    print(f"阈值获取错误: {e}")

            # 检查是否触摸了其他区域（切换彩色/二值化）
            elif ((not ENABLE_TOUCH_BUTTON or not self.is_point_in_rect(x, y, BUTTON_RECT)) and
                  (not ENABLE_THRESHOLD_TOOL or not self.is_point_in_rect(x, y, THRESHOLD_RECT)) and
                  (current_time - self.last_touch_time) > TOUCH_DEBOUNCE_MS):

                self.binary_mode = not self.binary_mode  # 切换二值化模式
                self.lab_mode = False  # 退出LAB模式
                self.threshold_mode = False  # 退出阈值模式
                self.last_touch_time = current_time
                mode_text = "开启" if self.binary_mode else "关闭"
                if not self.use_chinese:
                    mode_text = "ON" if self.binary_mode else "OFF"
                print(f"Touch switch: Binary mode {mode_text}")

    def draw_touch_button(self, img):
        """绘制触摸按钮"""
        if not ENABLE_TOUCH_BUTTON:
            return

        x, y, w, h = BUTTON_RECT

        # 根据当前模式选择按钮颜色
        if self.lab_mode:
            button_color = image.COLOR_BLUE
            text_color = image.COLOR_WHITE
        else:
            button_color = image.COLOR_GRAY
            text_color = image.COLOR_BLACK

        # 绘制按钮背景
        img.draw_rect(x, y, w, h, button_color, -1)
        # 绘制按钮边框
        img.draw_rect(x, y, w, h, image.COLOR_WHITE, 2)

        # 绘制按钮文字
        button_text = self.get_text("lab_btn")
        text_x = x + (w - len(button_text) * 6) // 2  # 居中显示
        text_y = y + (h - 12) // 2
        img.draw_string(text_x, text_y, button_text, text_color)

    def draw_threshold_button(self, img):
        """绘制阈值获取按钮"""
        if not ENABLE_THRESHOLD_TOOL:
            return

        x, y, w, h = THRESHOLD_RECT

        # 根据当前模式选择按钮颜色
        if self.threshold_mode:
            button_color = image.COLOR_RED
            text_color = image.COLOR_WHITE
        else:
            button_color = image.COLOR_GREEN
            text_color = image.COLOR_WHITE

        # 绘制按钮背景
        img.draw_rect(x, y, w, h, button_color, -1)
        # 绘制按钮边框
        img.draw_rect(x, y, w, h, image.COLOR_WHITE, 2)

        # 绘制按钮文字
        button_text = self.get_text("threshold_btn")
        text_x = x + (w - len(button_text) * 6) // 2  # 居中显示
        text_y = y + (h - 12) // 2
        img.draw_string(text_x, text_y, button_text, text_color)

    def draw_sample_rect(self, img):
        """绘制采样矩形"""
        if not self.threshold_mode or not self.sample_rect:
            return

        x, y = self.sample_rect
        size = SAMPLE_RECT_SIZE

        # 计算矩形边界
        x1 = max(0, x - size // 2)
        y1 = max(0, y - size // 2)
        x2 = min(img.width(), x + size // 2)
        y2 = min(img.height(), y + size // 2)

        # 绘制采样矩形
        img.draw_rect(x1, y1, x2 - x1, y2 - y1, image.COLOR_YELLOW, 3)

        # 绘制中心十字线
        img.draw_line(x - 10, y, x + 10, y, image.COLOR_YELLOW, 2)
        img.draw_line(x, y - 10, x, y + 10, image.COLOR_YELLOW, 2)

        # 显示采样信息
        info_text = f"Sample: {size}x{size}"
        img.draw_string(x1, y1 - 15, info_text, image.COLOR_YELLOW)

    def process_image(self, img):
        """图像处理"""
        if self.threshold_mode:
            # 阈值获取模式
            threshold_img = img.copy()

            # 显示阈值获取模式信息
            threshold_img.draw_string(10, 10, self.get_text("threshold_mode"), image.COLOR_RED)
            threshold_img.draw_string(10, 30, self.get_text("touch_tip"), image.COLOR_YELLOW)
            threshold_img.draw_string(10, 50, self.get_text("threshold_tip"), image.COLOR_YELLOW)

            # 绘制采样矩形
            self.draw_sample_rect(threshold_img)

            # 绘制所有按钮
            self.draw_touch_button(threshold_img)
            self.draw_threshold_button(threshold_img)

            return threshold_img
        elif self.lab_mode:
            # LAB模式处理
            lab_img = img.copy()
            blobs = self.detect_color_blobs(lab_img)

            # 转换为LAB色彩空间显示
            lab_img = lab_img.to_format(image.Format.FMT_LAB888)
            lab_img = lab_img.to_format(image.Format.FMT_RGB888)  # 转回RGB用于显示

            # 在LAB图像上绘制检测结果
            self.draw_blob_info(lab_img, blobs)

            # 显示模式信息
            lab_img.draw_string(10, 10, self.get_text("mode_lab"), image.COLOR_YELLOW)
            lab_img.draw_string(10, 30, self.get_text("touch_tip"), image.COLOR_YELLOW)
            lab_img.draw_string(10, 50, self.get_text("button_tip"), image.COLOR_YELLOW)
            lab_img.draw_string(10, 70, self.get_text("current_color"), image.COLOR_YELLOW)

            # 显示自定义阈值信息
            if self.custom_threshold:
                lab_img.draw_string(10, 90, "使用自定义阈值" if self.use_chinese else "Using Custom Threshold", image.COLOR_GREEN)

            # 绘制触摸按钮
            self.draw_touch_button(lab_img)
            self.draw_threshold_button(lab_img)

            return lab_img
        elif self.binary_mode:
            # 二值化处理
            binary_img = img.copy()
            blobs = self.detect_color_blobs(binary_img)

            # 根据当前检测颜色选择阈值
            if self.custom_threshold:
                threshold = self.custom_threshold
            elif self.current_color == "GREEN":
                threshold = GREEN_THRESHOLD
            else:
                threshold = WHITE_THRESHOLD

            # 创建二值化图像
            binary_img = binary_img.to_format(image.Format.FMT_GRAYSCALE)
            binary_img = binary_img.binary(threshold, invert=False)
            binary_img = binary_img.to_format(image.Format.FMT_RGB888)

            # 在二值化图像上绘制检测结果
            self.draw_blob_info(binary_img, blobs)

            # 显示模式信息
            binary_img.draw_string(10, 10, self.get_text("mode_binary"), image.COLOR_YELLOW)
            binary_img.draw_string(10, 30, self.get_text("touch_tip"), image.COLOR_YELLOW)
            binary_img.draw_string(10, 50, self.get_text("button_tip"), image.COLOR_YELLOW)
            binary_img.draw_string(10, 70, self.get_text("current_color"), image.COLOR_YELLOW)

            # 显示自定义阈值信息
            if self.custom_threshold:
                binary_img.draw_string(10, 90, "使用自定义阈值" if self.use_chinese else "Using Custom Threshold", image.COLOR_GREEN)

            # 绘制触摸按钮
            self.draw_touch_button(binary_img)
            self.draw_threshold_button(binary_img)

            return binary_img
        else:
            # 正常彩色模式
            blobs = self.detect_color_blobs(img)
            self.draw_blob_info(img, blobs)

            # 显示模式信息
            img.draw_string(10, 10, self.get_text("mode_color"), image.COLOR_YELLOW)
            img.draw_string(10, 30, self.get_text("touch_tip"), image.COLOR_YELLOW)
            img.draw_string(10, 50, self.get_text("button_tip"), image.COLOR_YELLOW)
            img.draw_string(10, 70, self.get_text("detected").format(len(blobs)), image.COLOR_YELLOW)
            img.draw_string(10, 90, self.get_text("current_color"), image.COLOR_YELLOW)

            # 显示自定义阈值信息
            if self.custom_threshold:
                img.draw_string(10, 110, "使用自定义阈值" if self.use_chinese else "Using Custom Threshold", image.COLOR_GREEN)

            # 绘制触摸按钮
            self.draw_touch_button(img)
            self.draw_threshold_button(img)

            return img

    def run(self):
        """主运行循环"""
        color_name = "白色" if self.current_color == "WHITE" else "绿色"
        color_name_en = "white" if self.current_color == "WHITE" else "green"

        if self.use_chinese:
            print(f"{color_name}色块检测程序启动...")
            print("触摸屏幕可切换彩色/二值化模式")
            if ENABLE_TOUCH_BUTTON:
                print("触摸按钮可切换LAB/二值化显示模式")
            if ENABLE_THRESHOLD_TOOL:
                print("触摸阈值按钮可进入阈值获取模式")
        else:
            print(f"{color_name_en.title()} blob detection started...")
            print("Touch screen to switch color/binary mode")
            if ENABLE_TOUCH_BUTTON:
                print("Touch button to switch LAB/Binary display mode")
            if ENABLE_THRESHOLD_TOOL:
                print("Touch threshold button to enter threshold capture mode")

        while not app.need_exit():
            try:
                # 读取摄像头图像
                img = self.cam.read()

                # 处理触摸事件
                self.handle_touch()

                # 处理图像
                processed_img = self.process_image(img)

                # 显示图像
                self.disp.show(processed_img)

                # 短暂延时以降低CPU使用率
                time.sleep_ms(FRAME_DELAY_MS)

            except Exception as e:
                error_msg = f"运行错误: {e}" if self.use_chinese else f"Runtime error: {e}"
                print(error_msg)
                time.sleep_ms(100)

        exit_msg = "程序退出" if self.use_chinese else "Program exit"
        print(exit_msg)

def main():
    """主函数"""
    try:
        detector = ColorBlobDetector()
        detector.run()
    except KeyboardInterrupt:
        print("User interrupted" if not hasattr(detector, 'use_chinese') or not detector.use_chinese else "用户中断程序")
    except Exception as e:
        print(f"Program exception: {e}")

if __name__ == "__main__":
    main()