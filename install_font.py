#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中文字体安装脚本
用于在MaixCAM设备上安装中文字体以解决显示问题
"""

import os
import sys

def check_font_exists():
    """检查字体文件是否存在"""
    font_paths = [
        "/maixapp/share/font/SourceHanSansCN-Regular.otf",
        "/maixapp/share/font/ppocr_keys_v1.ttf",
        "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
        "/system/fonts/DroidSansFallback.ttf"
    ]
    
    existing_fonts = []
    for font_path in font_paths:
        if os.path.exists(font_path):
            existing_fonts.append(font_path)
            print(f"✓ 找到字体: {font_path}")
    
    return existing_fonts

def test_font_loading():
    """测试字体加载"""
    try:
        from maix import image, display, app, time
        
        print("测试字体加载...")
        
        # 尝试加载字体
        font_paths = [
            "/maixapp/share/font/SourceHanSansCN-Regular.otf",
            "/maixapp/share/font/ppocr_keys_v1.ttf"
        ]
        
        font_loaded = False
        for font_path in font_paths:
            if os.path.exists(font_path):
                try:
                    image.load_font("test_font", font_path, size=20)
                    image.set_default_font("test_font")
                    print(f"✓ 成功加载字体: {font_path}")
                    font_loaded = True
                    break
                except Exception as e:
                    print(f"✗ 加载字体失败 {font_path}: {e}")
        
        if font_loaded:
            # 测试显示中文
            disp = display.Display()
            img = image.Image(disp.width(), disp.height())
            img.draw_string(10, 10, "中文测试 Chinese Test", image.COLOR_WHITE)
            img.draw_string(10, 40, "字体加载成功!", image.COLOR_GREEN)
            disp.show(img)
            
            print("✓ 中文字体测试成功!")
            print("显示屏上应该能看到中文字符")
            time.sleep(3)
        else:
            print("✗ 没有找到可用的中文字体")
            return False
            
        return True
        
    except ImportError:
        print("✗ 无法导入maix模块，请确保在MaixCAM设备上运行")
        return False
    except Exception as e:
        print(f"✗ 字体测试失败: {e}")
        return False

def create_font_directories():
    """创建字体目录"""
    font_dirs = [
        "/maixapp/share/font",
        "/usr/share/fonts/truetype",
        "/system/fonts"
    ]
    
    for font_dir in font_dirs:
        try:
            os.makedirs(font_dir, exist_ok=True)
            print(f"✓ 创建字体目录: {font_dir}")
        except Exception as e:
            print(f"✗ 创建目录失败 {font_dir}: {e}")

def download_font():
    """下载字体文件（如果网络可用）"""
    print("尝试下载中文字体...")
    
    # 这里可以添加字体下载逻辑
    # 由于MaixCAM可能没有网络连接，这里只是提示
    print("请手动将中文字体文件复制到以下目录之一:")
    print("- /maixapp/share/font/")
    print("- /usr/share/fonts/truetype/")
    print("- /system/fonts/")
    print()
    print("推荐字体文件:")
    print("- SourceHanSansCN-Regular.otf (思源黑体)")
    print("- NotoSansCJK-Regular.ttc (Noto字体)")
    print("- DroidSansFallback.ttf (Android字体)")

def main():
    """主函数"""
    print("=== MaixCAM 中文字体安装工具 ===")
    print()
    
    # 检查现有字体
    print("1. 检查现有字体文件...")
    existing_fonts = check_font_exists()
    
    if not existing_fonts:
        print("✗ 未找到中文字体文件")
        print()
        
        # 创建字体目录
        print("2. 创建字体目录...")
        create_font_directories()
        print()
        
        # 提供下载建议
        print("3. 字体安装建议...")
        download_font()
        print()
        
        print("请安装字体后重新运行此脚本进行测试")
        return
    
    # 测试字体加载
    print()
    print("2. 测试字体加载...")
    if test_font_loading():
        print()
        print("✓ 字体安装和测试成功!")
        print("现在可以正常运行main.py程序了")
    else:
        print()
        print("✗ 字体测试失败")
        print("请检查字体文件是否正确安装")

if __name__ == "__main__":
    main()
